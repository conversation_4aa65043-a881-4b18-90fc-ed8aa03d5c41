package rpcnode

import (
	"bytes"
	"context"
	"fmt"
	"net/http"
	"net/http/httputil"
	"net/url"
	"sentioxyz/sentio/service/usage/protos"
	"strings"
	"time"

	"google.golang.org/protobuf/types/known/timestamppb"

	redisrate "github.com/go-redis/redis_rate/v10"
	"github.com/grpc-ecosystem/grpc-gateway/v2/runtime"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	"google.golang.org/protobuf/encoding/protojson"

	"sentioxyz/sentio/chain/evm/chaininfo"
	"sentioxyz/sentio/common/log"
	commonModel "sentioxyz/sentio/service/common/models"
	commonProtos "sentioxyz/sentio/service/common/protos"
	"sentioxyz/sentio/service/rpcnode/model"
)

const RpcNodeCallSku = "rpc_node_call"

func (e *Service) Proxy(r *http.Request, w http.ResponseWriter) error {
	ctx := r.Context()

	var node *model.RPCNode
	var network *model.RPCNetwork
	var rpcEndpoint *model.RPCEndpoint
	var proxy *httputil.ReverseProxy
	var err error

	// Check if this is an internal RPC node request
	if e.isInternalRequest(r) {
		// Parse URL path: /{processorId}/{code}/{slug}
		pathParts := strings.SplitN(strings.TrimPrefix(r.URL.Path, "/"), "/", 3)
		if len(pathParts) < 3 {
			return status.Errorf(codes.InvalidArgument, "invalid internal RPC node URL format")
		}

		processorID := pathParts[0]
		code := pathParts[1]
		slug := pathParts[2]

		// Verify the code matches the processor ID
		expectedCode := e.computeProcessorCode(processorID)
		if code != expectedCode {
			return status.Errorf(codes.Unauthenticated, "invalid internal RPC node code")
		}

		// Get processor to find project ID
		processor, err := e.repo.GetProcessor(ctx, processorID)
		if err != nil || processor == nil {
			return status.Errorf(codes.NotFound, "processor not found: %v", err)
		}

		// Find the internal RPC node for this project
		node, err = e.repo.FindInternalRPCNodeByProjectID(ctx, processor.ProjectID)
		if err != nil {
			return status.Errorf(codes.Internal, "failed to find internal RPC node: %v", err)
		}
		if node == nil || !node.Enabled {
			return status.Errorf(codes.NotFound, "internal RPC node not found or disabled")
		}

		// Get RPC proxy for the slug
		network, rpcEndpoint, proxy, err = e.GetRPCProxy(node, slug)
		if err != nil {
			return err
		}
	} else {
		// Original public RPC node logic
		code, slug, _ := strings.Cut(strings.TrimPrefix(r.URL.Path, "/"), "/")

		node, err = e.repo.FindRPCNodeByCode(ctx, code)
		if err != nil {
			return err
		}
		if node == nil || !node.Enabled {
			return status.Errorf(codes.NotFound, "rpc node %s not found", code)
		}

		if node.ForkID != "" {
			network, rpcEndpoint, proxy, err = e.getForkProxy(node)
		} else {
			network, rpcEndpoint, proxy, err = e.GetRPCProxy(node, slug)
		}
		if err != nil {
			return err
		}
	}

	// Common proxy logic for all request types
	err = e.rateLimitCheck(ctx, node, rpcEndpoint)
	if err != nil {
		return err
	}
	err = e.overUsageCheck(ctx, node, rpcEndpoint)
	if err != nil {
		return err
	}

	rCtx := ctx.Value("rCtx").(*requestContext)
	rCtx.node = node
	rCtx.endpoint = rpcEndpoint
	rCtx.network = network

	proxy.ServeHTTP(w, r)
	return nil
}

func (e *Service) getForkProxy(node *model.RPCNode) (network *model.RPCNetwork, rpcEndpoint *model.RPCEndpoint, proxy *httputil.ReverseProxy, err error) {
	// handle fork node
	network = &model.RPCNetwork{
		ID:   node.Network,
		Name: node.Network,
		Endpoints: []model.RPCEndpoint{
			{
				Path:        "/",
				Name:        node.ForkID,
				Upstream:    node.ForkNodeUrl,
				RateLimitID: "default",
			},
		},
	}
	rpcEndpoint = &network.Endpoints[0]

	key := fmt.Sprintf("fork/%s", node.ForkID)
	var exists bool
	proxy, exists = e.proxies[key]
	if !exists {
		proxy, err = e.createProxy(node.ForkNodeUrl)
		if err != nil {
			return
		}
		e.proxies[key] = proxy
	}
	return
}

func (e *Service) GetRPCProxy(node *model.RPCNode, slug string) (network *model.RPCNetwork, rpcEndpoint *model.RPCEndpoint, proxy *httputil.ReverseProxy, err error) {
	// Extract network slug from the beginning of the slug path
	// Format: {network_slug}/path or just network_slug for universal endpoints
	slugParts := strings.SplitN(strings.TrimPrefix(slug, "/"), "/", 2)
	if len(slugParts) == 0 {
		err = status.Errorf(codes.InvalidArgument, "invalid slug format")
		return
	}

	networkSlug := slugParts[0]

	var networkID string

	// First, try to find network by slug in settings.networks for backward compatibility
	for _, n := range e.settings.Networks {
		for _, ep := range n.Endpoints {
			if strings.TrimPrefix(ep.Path, "/") == networkSlug {
				networkID = n.ID
				network = &n
				rpcEndpoint = &ep
				break
			}
		}
	}

	// If not found in settings, try to resolve using chaininfo
	if network == nil {
		chainInfo, exists := chaininfo.SlugToInfo[networkSlug]
		if !exists {
			err = status.Errorf(codes.NotFound, "unknown network slug: %s", networkSlug)
			return
		}
		networkID = chainInfo.ChainID
		// user the default upstream from settings to proxy the request
		network = &model.RPCNetwork{
			ID:   networkID,
			Name: chainInfo.Name,
			Endpoints: []model.RPCEndpoint{
				{
					Path:        "/" + chainInfo.Slug,
					Name:        chainInfo.Name,
					Upstream:    e.settings.DefaultUpstream + "/" + chainInfo.Slug,
					RateLimitID: "default",
				},
			},
		}
		rpcEndpoint = &network.Endpoints[0]
	}

	if rpcEndpoint == nil {
		err = status.Errorf(codes.NotFound, "network configuration not found for slug: %s (chain ID: %s)", networkSlug, networkID)
		return
	}

	// Check if this network is allowed for this RPC node
	if !node.IsNetworkAllowed(networkID) {
		err = status.Errorf(codes.PermissionDenied, "network %s (chain ID: %s) not allowed for this RPC node", networkSlug, networkID)
		return
	}

	// Get or create proxy
	proxyKey := fmt.Sprintf("%s/%s", network.ID, rpcEndpoint.Path)
	proxy = e.proxies[proxyKey]
	if proxy == nil {
		// Use default upstream if endpoint upstream is empty
		upstream := rpcEndpoint.Upstream
		if upstream == "" && e.settings.DefaultUpstream != "" {
			upstream = e.settings.DefaultUpstream + rpcEndpoint.Path
		}

		// Create new proxy
		proxy, err = e.createProxy(upstream)
		if err != nil {
			err = status.Errorf(codes.Internal, "failed to create proxy for %s: %v", upstream, err)
			return
		}
		e.proxies[proxyKey] = proxy
	}

	return
}

func (e *Service) rateLimitCheck(ctx context.Context, node *model.RPCNode, rpcEndpoint *model.RPCEndpoint) error {
	if e.settings.RateLimits != nil {
		if rl, ok := e.settings.RateLimits[rpcEndpoint.RateLimitID]; ok {
			var rateLimit model.Limit
			tier := node.Project.Tier()
			switch tier {
			case commonProtos.Tier_ENTERPRISE:
				rateLimit = rl.Enterprise
			case commonProtos.Tier_PRO:
				rateLimit = rl.Pro
			case commonProtos.Tier_DEV:
				rateLimit = rl.Dev
			default:
				rateLimit = rl.Free
			}
			key := fmt.Sprintf("%s/%s", node.ID, rpcEndpoint.Path)
			limit := redisrate.Limit{
				Rate:   rateLimit.Rate,
				Burst:  rateLimit.Burst,
				Period: time.Second,
			}
			limit.Period, _ = time.ParseDuration(rateLimit.Period)
			if res, err := e.limiter.Allow(ctx, key, limit); err == nil {
				if res.Allowed <= 0 {
					return status.Errorf(codes.ResourceExhausted, "rate limit exceeded %d/%s, retry after %s", rateLimit.Rate, rateLimit.Period, res.RetryAfter.String())
				}
			}
		}
	}

	return nil
}

func (e *Service) Handle(h *runtime.ServeMux) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {

		start := time.Now()
		if strings.HasPrefix(r.URL.Path, "/api/v1") || strings.HasPrefix(r.URL.Path, "/healthz") {
			h.ServeHTTP(w, r)
			return
		}
		rCtx := &requestContext{
			start:        start,
			method:       r.Method,
			originUrl:    r.URL.String(),
			requestBody:  &bytes.Buffer{},
			responseBody: &bytes.Buffer{},
		}
		ctx := context.WithValue(r.Context(), "rCtx", rCtx)
		clonedR := r.Clone(ctx)
		log.Debugw("received request", "method", r.Method, "url", r.URL.String())
		err := e.Proxy(clonedR, w)
		if err != nil {
			runtime.DefaultHTTPErrorHandler(ctx, h, &runtime.JSONPb{
				MarshalOptions: protojson.MarshalOptions{
					EmitUnpopulated: true,
				},
				UnmarshalOptions: protojson.UnmarshalOptions{
					DiscardUnknown: true,
				}}, w, r, err)
		}
	})
}

func (e *Service) Director(req *http.Request) {
	rCtx := req.Context().Value("rCtx").(*requestContext)
	target, _ := url.Parse(rCtx.endpoint.Upstream)
	targetQuery := target.RawQuery
	originQuery := req.URL.RawQuery

	_, slug, _ := strings.Cut(strings.TrimPrefix(req.URL.Path, "/"), "/")
	relativePath := strings.TrimPrefix(slug, strings.TrimPrefix(rCtx.endpoint.Path, "/"))
	req.URL = target.JoinPath(relativePath)

	if targetQuery == "" || originQuery == "" {
		req.URL.RawQuery = targetQuery + originQuery
	} else {
		req.URL.RawQuery = targetQuery + "&" + originQuery
	}
	rCtx.targetUrl = req.URL.String()

	if req.Header.Get("X-Forwarded-Host") == "" {
		req.Header.Set("X-Forwarded-Host", req.Host)
	}

	req.Host = target.Host

	log.Debugw("proxying request", "method", rCtx.method, "url", rCtx.originUrl, "target", rCtx.targetUrl)
	if req.Body != nil {
		req.Body = NewCopyReader(req.Body, rCtx.requestBody)
	}
}

func (e *Service) logResponse(response *http.Response) error {
	rCtx := response.Request.Context().Value("rCtx").(*requestContext)
	reader := NewCopyReader(response.Body, rCtx.responseBody)

	reader.CloseCallback = func() error {
		rCtx.LogResponse(response)
		return nil
	}

	response.Body = reader

	e.usageClient.AsyncSave(response.Request.Context(), commonModel.AnonymousIdentity(), rCtx.node.Project,
		RpcNodeCallSku, time.Now(), response.StatusCode < 300, false)

	return nil
}

func (e *Service) overUsageCheck(ctx context.Context, node *model.RPCNode, rpcEndpoint *model.RPCEndpoint) error {
	key := fmt.Sprintf("%s/%s:overChecked", node.ID, rpcEndpoint.Path)

	// 1. Check if there's a cached value in Redis
	val, err := e.redisCli.Get(ctx, key).Result()
	if err == nil {
		// Value exists in cache
		if val == "" {
			// Empty string means no not over limit
			return nil
		}
		// Non-empty string means there was an error (over limit)
		return status.Errorf(codes.ResourceExhausted, val)
	}

	// 2. If not cached, call CheckOverLimit
	resp, err := e.usageClient.CheckOverLimit(ctx, &protos.CheckOverLimitRequest{
		ProjectId: node.Project.ID,
		Sku:       RpcNodeCallSku,
		Now:       timestamppb.Now(),
	})

	if err != nil {
		// Cache the error message
		e.redisCli.Set(ctx, key, err.Error(), 5*time.Minute)
		return err
	}

	if resp != nil && len(resp.Over) > 0 {
		// Over limit, cache the error message
		errorMsg := fmt.Sprintf("over limit: %s", resp.Over[0])
		e.redisCli.Set(ctx, key, errorMsg, 5*time.Minute)
		return status.Errorf(codes.ResourceExhausted, errorMsg)
	}

	// Not over limit, cache empty string to indicate success
	e.redisCli.Set(ctx, key, "", 5*time.Minute)
	return nil
}

func (e *Service) isInternalRequest(r *http.Request) bool {
	// parse the internal URL from settings
	if e.settings.InternalURL == "" {
		return false
	}
	parsedURL, err := url.Parse(e.settings.InternalURL)
	if err != nil {
		log.Errorw("failed to parse internal URL", "url", e.settings.InternalURL, "error", err)
		return false
	}
	// Check if the request host matches the internal URL host
	reqHost := r.Header.Get("X-Forwarded-Host")
	if reqHost == "" {
		reqHost = r.Host
	}

	return reqHost == parsedURL.Host
}


